#!/usr/bin/env python3
"""
Show all chunks stored in a specific project's RAG system.
"""

import os
import psycopg2
from app.managers.DocumentManager import DocumentManager

def show_project_chunks(project_id: str):
    """Show all chunks for a specific project."""
    
    print(f"🔍 Showing chunks for project: {project_id}")
    print("=" * 60)
    
    try:
        # Create DocumentManager for the specific project
        doc_manager = DocumentManager(project_id=project_id)
        
        if not doc_manager.vector_store:
            print("❌ Vector store not initialized")
            return False
        
        # Connect directly to PostgreSQL to see what's stored
        db_config = {
            "host": os.getenv("POSTGRES_HOST", "localhost"),
            "port": os.getenv("POSTGRES_PORT", "5432"),
            "database": os.getenv("POSTGRES_DB", "kairos_graphrag"),
            "user": os.getenv("POSTGRES_USER", "postgres"),
            "password": os.getenv("POSTGRES_PASSWORD", "password")
        }
        
        conn = psycopg2.connect(**db_config)
        cur = conn.cursor()
        
        # Check what collections exist
        print("📚 Available collections:")
        cur.execute("SELECT name, uuid FROM langchain_pg_collection;")
        collections = cur.fetchall()
        
        if not collections:
            print("   No collections found")
            return False
        
        for name, uuid in collections:
            print(f"   - {name} (UUID: {uuid})")
        
        # Find the collection for our project
        collection_name = f"documents_{project_id}".replace("-", "_")
        print(f"\n🎯 Looking for collection: {collection_name}")
        
        cur.execute("SELECT uuid FROM langchain_pg_collection WHERE name = %s;", (collection_name,))
        collection_result = cur.fetchone()
        
        if not collection_result:
            print(f"❌ Collection '{collection_name}' not found")
            print("Available collections:")
            for name, uuid in collections:
                print(f"   - {name}")
            return False
        
        collection_uuid = collection_result[0]
        print(f"✅ Found collection UUID: {collection_uuid}")
        
        # Get all embeddings/chunks for this collection
        print(f"\n📄 Chunks in collection '{collection_name}':")
        cur.execute("""
            SELECT document, cmetadata 
            FROM langchain_pg_embedding 
            WHERE collection_id = %s
            ORDER BY id;
        """, (collection_uuid,))
        
        chunks = cur.fetchall()
        
        if not chunks:
            print("   No chunks found in this collection")
            return False
        
        print(f"   Found {len(chunks)} chunks:")
        print("-" * 60)
        
        for i, (document, metadata) in enumerate(chunks, 1):
            print(f"\n📝 Chunk {i}:")
            print(f"Content: {document[:200]}...")
            if len(document) > 200:
                print(f"         (Total length: {len(document)} characters)")
            
            if metadata:
                print(f"Metadata: {metadata}")
            
            print("-" * 40)
        
        # Test some searches on this specific project
        print(f"\n🔍 Testing searches on project {project_id}:")
        test_queries = ["IVF", "fertility", "treatment", "process", "medical"]
        
        for query in test_queries:
            print(f"\n🔎 Searching for: '{query}'")
            try:
                results = doc_manager.vector_search(query, k=3)
                print(f"   Found {len(results)} results")
                
                for j, result in enumerate(results, 1):
                    print(f"   {j}. Score: {result['score']:.3f}")
                    print(f"      Content: {result['content'][:100]}...")
            except Exception as e:
                print(f"   Search error: {e}")
        
        cur.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_all_projects():
    """Show chunks for all projects."""
    
    try:
        db_config = {
            "host": os.getenv("POSTGRES_HOST", "localhost"),
            "port": os.getenv("POSTGRES_PORT", "5432"),
            "database": os.getenv("POSTGRES_DB", "kairos_graphrag"),
            "user": os.getenv("POSTGRES_USER", "postgres"),
            "password": os.getenv("POSTGRES_PASSWORD", "password")
        }
        
        conn = psycopg2.connect(**db_config)
        cur = conn.cursor()
        
        print("🗂️  All collections in database:")
        cur.execute("SELECT name, uuid FROM langchain_pg_collection;")
        collections = cur.fetchall()
        
        for name, uuid in collections:
            print(f"   - {name} (UUID: {uuid})")
            
            # Count chunks in each collection
            cur.execute("SELECT COUNT(*) FROM langchain_pg_embedding WHERE collection_id = %s;", (uuid,))
            count = cur.fetchone()[0]
            print(f"     Chunks: {count}")
        
        cur.close()
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    # Your specific project ID
    project_id = "4191a198-b6df-4a5c-afaf-89d0c11b47e1"
    
    print("🚀 RAG Chunk Inspector")
    print("=" * 60)
    
    # Show all projects first
    show_all_projects()
    
    print("\n" + "=" * 60)
    
    # Show specific project
    success = show_project_chunks(project_id)
    
    if success:
        print(f"\n🎉 Chunk inspection completed for project {project_id}!")
    else:
        print(f"\n💥 Chunk inspection failed for project {project_id}!")
