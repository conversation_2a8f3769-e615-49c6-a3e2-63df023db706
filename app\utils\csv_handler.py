import pandas as pd
from pathlib import Path

def simple_csv_metadata(file_path: str) -> str:
    """Super simple CSV metadata - just df.info() and df.describe()"""
    try:
        df = pd.read_csv(file_path)
        
        # Basic info
        info_str = f"CSV File: {Path(file_path).name}\n"
        info_str += f"Location: {file_path}\n"
        info_str += f"Shape: {df.shape[0]} rows × {df.shape[1]} columns\n\n"
        
        # df.info() equivalent
        info_str += "COLUMN INFO:\n"
        for col in df.columns:
            dtype = str(df[col].dtype)
            non_null = df[col].count()
            total = len(df)
            info_str += f"  {col}: {dtype} ({non_null}/{total} non-null)\n"
        
        # df.describe() for numeric columns
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            info_str += f"\nNUMERIC SUMMARY:\n"
            desc = df[numeric_cols].describe()
            info_str += desc.to_string()
        
        # Sample data
        info_str += f"\n\nSAMPLE DATA (first 3 rows):\n"
        info_str += df.head(3).to_string()
        
        return info_str
        
    except Exception as e:
        return f"Error reading CSV {file_path}: {str(e)}"

def process_csv_simple(file_path: str, project_id: str) -> dict:
    """Process CSV and save metadata to vector DB"""
    try:
        # Get simple metadata
        metadata_text = simple_csv_metadata(file_path)

        # Add to vector DB
        from app.managers.DocumentManager import DocumentManager
        doc_manager = DocumentManager(project_id=project_id)

        result = doc_manager.process_text_content(
            content=metadata_text,
            filename=f"csv_{Path(file_path).stem}",
            metadata={
                "file_type": "csv_metadata",
                "filename": Path(file_path).name,
                "file_path": file_path
            }
        )

        return {
            "success": True,
            "message": f"CSV metadata saved: {Path(file_path).name}",
            "metadata": metadata_text
        }

    except Exception as e:
        return {
            "success": False,
            "error": f"Failed to process CSV: {str(e)}"
        }
