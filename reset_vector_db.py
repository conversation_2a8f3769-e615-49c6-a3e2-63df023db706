#!/usr/bin/env python3
"""
Reset the vector database to fix schema issues.
Run this script to clean up and recreate the vector database tables.
"""

import os
import psycopg2
from app.managers.DocumentManager import DocumentManager

def reset_vector_database():
    """Reset the vector database tables."""
    
    # Database config
    db_config = {
        "host": os.getenv("POSTGRES_HOST", "localhost"),
        "port": os.getenv("POSTGRES_PORT", "5432"),
        "database": os.getenv("POSTGRES_DB", "kairos_graphrag"),
        "user": os.getenv("POSTGRES_USER", "postgres"),
        "password": os.getenv("POSTGRES_PASSWORD", "password")
    }
    
    try:
        print("🗄️  Connecting to PostgreSQL...")
        conn = psycopg2.connect(**db_config)
        cur = conn.cursor()
        
        print("🧹 Checking vector tables...")
        # Check if tables exist
        cur.execute("SELECT table_name FROM information_schema.tables WHERE table_name IN ('langchain_pg_embedding', 'langchain_pg_collection');")
        existing_tables = cur.fetchall()

        if existing_tables:
            print(f"✅ Found existing tables: {[t[0] for t in existing_tables]}")
            print("⚠️  Tables will be preserved to keep your data!")
        else:
            print("📝 No existing vector tables found - will be created automatically")
        
        conn.commit()
        cur.close()
        conn.close()
        
        print("🔧 Creating DocumentManager to ensure tables exist...")
        # Create a DocumentManager instance - this will ensure tables exist
        DocumentManager(project_id="test")

        print("✅ Vector database check complete!")
        print("\nNext steps:")
        print("1. Upload documents to your project")
        print("2. Test the search functionality")
        
    except Exception as e:
        print(f"❌ Error resetting database: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Resetting vector database...")
    print("=" * 50)
    
    success = reset_vector_database()
    
    if success:
        print("\n🎉 Database reset completed successfully!")
    else:
        print("\n💥 Database reset failed!")
