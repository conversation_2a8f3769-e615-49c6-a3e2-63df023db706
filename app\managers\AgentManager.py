from ..agents.BaseAgent import BaseAgent
from typing import Dict, Any

class AgentManager:
    def __init__(self, project_id: str = None):
        self.agents = {}  # name -> agent instance
        self.project_id = project_id
        self.default_agent = "base"

        # Initialize default agent
        self._initialize_default_agents()

    def _initialize_default_agents(self):
        """Initialize default agents."""
        try:
            # Create base agent for this project with all capabilities
            base_agent = BaseAgent(project_id=self.project_id)
            self.register_agent("base", base_agent)
        except Exception as e:
            print(f"Warning: Could not initialize base agent: {e}")

    def register_agent(self, name: str, agent_instance):
        """Register an agent with a given name."""
        self.agents[name] = agent_instance

    def list_agents(self):
        """List all registered agent names."""
        return list(self.agents.keys())

    def get_agent(self, name: str):
        """Get a specific agent by name."""
        return self.agents.get(name)

    def generate_response(self, message: str, context: Dict[str, Any] = None, agent_name: str = None) -> str:
        """
        Generate a response using the specified agent or default agent.

        Args:
            message: The input message
            context: Optional context information
            agent_name: Name of the agent to use (defaults to Graph-RAG agent)

        Returns:
            Agent response as string
        """
        # Determine which agent to use
        selected_agent_name = agent_name or self.default_agent

        if selected_agent_name not in self.agents:
            # Fallback to any available agent or return simple response
            if self.agents:
                selected_agent_name = list(self.agents.keys())[0]
            else:
                return f"I received your message: '{message}'. Please upload some documents first so I can help you better."

        try:
            agent = self.agents[selected_agent_name]
            response = agent.reply(message, context)
            return response
        except Exception as e:
            # Better error handling - don't expose technical details
            return f"I'm having trouble processing your request right now. Please try uploading some documents first, or check if the system is properly configured."

    def process_document(self, file_path: str, metadata: Dict = None, agent_name: str = None) -> Dict:
        """
        Process a document using the specified agent's document processing capabilities.

        Args:
            file_path: Path to the document
            metadata: Optional metadata
            agent_name: Name of the agent to use (defaults to Graph-RAG agent)

        Returns:
            Processing results
        """
        selected_agent_name = agent_name or self.default_agent

        if selected_agent_name not in self.agents:
            return {"error": f"Agent {selected_agent_name} not found"}

        agent = self.agents[selected_agent_name]

        # Check if agent has document processing capability
        if hasattr(agent, 'process_document_tool'):
            try:
                result = agent.process_document_tool(file_path, metadata)
                return {"status": "success", "result": result}
            except Exception as e:
                return {"status": "error", "error": str(e)}
        else:
            return {"error": f"Agent {selected_agent_name} does not support document processing"}

    def get_agent_stats(self, agent_name: str = None) -> Dict:
        """Get statistics for a specific agent or all agents."""
        if agent_name:
            if agent_name in self.agents:
                agent = self.agents[agent_name]
                if hasattr(agent, 'get_stats'):
                    return agent.get_stats()
                else:
                    return {"agent": agent_name, "stats": "Not available"}
            else:
                return {"error": f"Agent {agent_name} not found"}
        else:
            # Return stats for all agents
            stats = {}
            for name, agent in self.agents.items():
                if hasattr(agent, 'get_stats'):
                    stats[name] = agent.get_stats()
                else:
                    stats[name] = {"stats": "Not available"}
            return stats
