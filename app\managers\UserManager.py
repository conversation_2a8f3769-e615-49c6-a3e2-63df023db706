import sqlite3
from pathlib import Path
import uuid
from typing import List, Optional
from .ProjectManager import ProjectManager

class UserManager:
    """Manager for users and their projects, backed by SQLite."""
    def __init__(self, db_path="kairos.db"):
        self.db_path = db_path
        self._init_db()

    def _init_db(self):
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id TEXT PRIMARY KEY,
                    username TEXT NOT NULL
                )
            ''')
            c.execute('''
                CREATE TABLE IF NOT EXISTS projects (
                    project_id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    project_name TEXT,
                    FOREIGN KEY(user_id) REFERENCES users(user_id)
                )
            ''')
            conn.commit()

    def create_user(self, username: str) -> str:
        user_id = str(uuid.uuid4())
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('INSERT INTO users (user_id, username) VALUES (?, ?)', (user_id, username))
            conn.commit()
        return user_id

    def get_user(self, user_id: str) -> Optional[dict]:
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('SELECT user_id, username FROM users WHERE user_id = ?', (user_id,))
            row = c.fetchone()
            if row:
                return {"user_id": row[0], "username": row[1]}
            return None

    def list_users(self) -> List[dict]:
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('SELECT user_id, username FROM users')
            rows = c.fetchall()
            return [{"user_id": row[0], "username": row[1]} for row in rows]

    def create_project_for_user(self, user_id: str, project_name: Optional[str] = None) -> Optional[dict]:
        if not self.get_user(user_id):
            return None
        project_id = str(uuid.uuid4())
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('INSERT INTO projects (project_id, user_id, project_name) VALUES (?, ?, ?)', (project_id, user_id, project_name))
            conn.commit()
        # Create the project folder and initialize files
        ProjectManager(project_name=project_name, project_id=project_id)
        return {"project_id": project_id, "project_name": project_name}

    def get_projects_for_user(self, user_id: str) -> List[dict]:
        with sqlite3.connect(self.db_path) as conn:
            c = conn.cursor()
            c.execute('SELECT project_id, project_name FROM projects WHERE user_id = ?', (user_id,))
            rows = c.fetchall()
            return [{"project_id": row[0], "project_name": row[1]} for row in rows]

    def project_exists(self, project_id: str, base_dir: str = "./projects") -> bool:
        # This still checks the filesystem for project folder existence
        project_path = Path(base_dir) / project_id
        return project_path.exists() and project_path.is_dir() 