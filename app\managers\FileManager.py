from pathlib import Path
from typing import List, Dict, Optional
import pandas as pd
import json
from git import Repo, Actor
from datetime import datetime

class FileManager:
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.input_dir = self.project_path / "input"
        self.output_dir = self.project_path / "output"
        self.logs_dir = self.project_path / "logs"
        self._create_dirs()
        self.repo = self._init_or_load_repo()
        self.current_branch = self.repo.active_branch.name if self.repo.head.is_valid() else "main"

    def _create_dirs(self):
        try:
            self.project_path.mkdir(parents=True, exist_ok=True)  # Ensure project root exists
            self.input_dir.mkdir(parents=True, exist_ok=True)
            self.output_dir.mkdir(parents=True, exist_ok=True)
            self.logs_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            print(f"Error creating directories: {e}")
            # Optionally, handle/log the error or raise a custom exception

    def _init_or_load_repo(self):
        try:
            return Repo(self.project_path)
        except:
            return Repo.init(self.project_path)

    # File operations
    def save_file(self, filename: str, content, location: str = "output") -> str:
        if location == "input":
            file_path = self.input_dir / filename
        elif location == "output":
            file_path = self.output_dir / filename
        else:
            raise ValueError("Location must be 'input' or 'output'")
        if isinstance(content, pd.DataFrame):
            if filename.endswith('.csv'):
                content.to_csv(file_path, index=False)
            elif filename.endswith(('.xlsx', '.xls')):
                content.to_excel(file_path, index=False)
            else:
                content.to_csv(file_path.with_suffix('.csv'), index=False)
        elif isinstance(content, (dict, list)):
            file_path.write_text(json.dumps(content, indent=2))
        else:
            file_path.write_text(str(content))
        return str(file_path)

    def list_files(self, location: str = "output") -> List[str]:
        if location == "input":
            return [f.name for f in self.input_dir.glob("*") if f.is_file()]
        elif location == "output":
            return [f.name for f in self.output_dir.glob("*") if f.is_file()]
        else:
            raise ValueError("Location must be 'input' or 'output'")

    # Git operations
    def commit(self, message: str) -> Optional[str]:
        try:
            self.repo.git.add(A=True)
            if self.repo.is_dirty() or self.repo.untracked_files:
                actor = Actor("AgentSystem", "<EMAIL>")
                commit = self.repo.index.commit(
                    f"{message} | {self.current_branch}",
                    author=actor,
                    committer=actor
                )
                return commit.hexsha
            else:
                return self.repo.head.commit.hexsha
        except Exception:
            return None

    def create_branch(self, branch_name: str, from_commit: Optional[str] = None):
        if from_commit:
            return self.repo.create_head(branch_name, from_commit)
        else:
            return self.repo.create_head(branch_name)

    def checkout_branch(self, branch_name: str):
        self.repo.git.checkout(branch_name)
        self.current_branch = branch_name

    def list_branches(self) -> List[str]:
        return [branch.name for branch in self.repo.branches]

    def get_commit_history(self) -> List[Dict]:
        commits = []
        for commit in self.repo.iter_commits():
            commits.append({
                "hash": commit.hexsha,
                "short_hash": commit.hexsha[:8],
                "message": commit.message.strip(),
                "author": str(commit.author),
                "date": commit.committed_datetime.isoformat()
            })
        return commits

    def create_new_project(self, project_id: str, project_name: str):
        # Create .gitignore
        gitignore = """logs/
                    *.log
                    __pycache__/
                    *.pyc
                    .env
                    .DS_Store
                    *.tmp"""
        (self.project_path / ".gitignore").write_text(gitignore)
        # Create metadata
        metadata = {
            "project_id": project_id,
            "project_name": project_name,
            "created_at": datetime.now().isoformat()
        }
        (self.project_path / ".project_meta.json").write_text(json.dumps(metadata, indent=2))
        # Create empty conversation file
        conversation_file = self.project_path / "conversation.json"
        conversation_file.write_text(json.dumps([], indent=2))

    def load_existing_project(self):
        # Load metadata
        meta_file = self.project_path / ".project_meta.json"
        metadata = None
        if meta_file.exists():
            metadata = json.loads(meta_file.read_text())
        # Load conversation
        conversation_file = self.project_path / "conversation.json"
        messages = []
        if conversation_file.exists():
            try:
                messages = json.loads(conversation_file.read_text())
            except:
                messages = []
        return metadata, messages 