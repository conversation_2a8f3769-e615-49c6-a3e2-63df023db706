from fastapi import APIRouter, HTTPException
from typing import List
from app.managers.UserManager import UserManager
from app.models.User import User

router = APIRouter()
user_manager = UserManager()

@router.post("/users", response_model=dict)
def create_user(username: str):
    user_id = user_manager.create_user(username)
    return {"user_id": user_id}

@router.get("/users", response_model=List[dict])
def list_users():
    return user_manager.list_users()

@router.post("/users/{user_id}/new_project", response_model=dict)
def create_project_for_user(user_id: str, project_name: str = None):
    project = user_manager.create_project_for_user(user_id, project_name)
    if not project:
        raise HTTPException(status_code=404, detail="User not found")
    return {"project_id": project["project_id"], "project_name": project["project_name"]}

@router.get("/users/{user_id}/projects", response_model=List[dict])
def list_projects_for_user(user_id: str):
    projects = user_manager.get_projects_for_user(user_id)
    return projects

