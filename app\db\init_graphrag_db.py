#!/usr/bin/env python3
"""
Database initialization script for Graph-RAG functionality.
This script sets up PostgreSQL with pgvector extension and creates necessary tables.
"""

import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import sys

def get_db_config():
    """Get database configuration from environment variables."""
    return {
        "host": os.getenv("POSTGRES_HOST", "localhost"),
        "port": os.getenv("POSTGRES_PORT", "5432"),
        "database": os.getenv("POSTGRES_DB", "kairos_graphrag"),
        "user": os.getenv("POSTGRES_USER", "postgres"),
        "password": os.getenv("POSTGRES_PASSWORD", "password")
    }

def create_database_if_not_exists(config):
    """Create the database if it doesn't exist."""
    try:
        # Connect to PostgreSQL server (not to specific database)
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            database="postgres"  # Connect to default postgres database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cur = conn.cursor()
        
        # Check if database exists
        cur.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", (config["database"],))
        exists = cur.fetchone()
        
        if not exists:
            print(f"Creating database: {config['database']}")
            cur.execute(f'CREATE DATABASE "{config["database"]}"')
            print(f"Database {config['database']} created successfully")
        else:
            print(f"Database {config['database']} already exists")
        
        cur.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error creating database: {e}")
        return False

def setup_pgvector_extension(config):
    """Set up pgvector extension in the database."""
    try:
        conn = psycopg2.connect(**config)
        cur = conn.cursor()
        
        # Create pgvector extension
        print("Creating pgvector extension...")
        cur.execute("CREATE EXTENSION IF NOT EXISTS vector;")
        
        conn.commit()
        cur.close()
        conn.close()
        print("pgvector extension created successfully")
        return True
        
    except Exception as e:
        print(f"Error setting up pgvector extension: {e}")
        return False

def create_base_tables(config):
    """Create base tables for Graph-RAG functionality."""
    try:
        conn = psycopg2.connect(**config)
        cur = conn.cursor()
        
        # Create base triples table (project-specific tables will be created dynamically)
        print("Creating base triples table...")
        cur.execute("""
            CREATE TABLE IF NOT EXISTS triples (
                id SERIAL PRIMARY KEY,
                subject TEXT NOT NULL,
                predicate TEXT NOT NULL,
                object TEXT NOT NULL,
                chunk_id TEXT,
                project_id TEXT,
                confidence FLOAT DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # Create indexes for better performance
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_triples_subject ON triples (subject);
        """)
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_triples_predicate ON triples (predicate);
        """)
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_triples_project_id ON triples (project_id);
        """)
        
        # Create documents metadata table
        print("Creating documents metadata table...")
        cur.execute("""
            CREATE TABLE IF NOT EXISTS documents (
                id SERIAL PRIMARY KEY,
                file_path TEXT NOT NULL,
                project_id TEXT,
                chunks_count INTEGER DEFAULT 0,
                triples_count INTEGER DEFAULT 0,
                processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata JSONB
            );
        """)
        
        cur.execute("""
            CREATE INDEX IF NOT EXISTS idx_documents_project_id ON documents (project_id);
        """)
        
        conn.commit()
        cur.close()
        conn.close()
        print("Base tables created successfully")
        return True
        
    except Exception as e:
        print(f"Error creating base tables: {e}")
        return False

def test_connection(config):
    """Test the database connection and verify setup."""
    try:
        conn = psycopg2.connect(**config)
        cur = conn.cursor()
        
        # Test pgvector extension
        cur.execute("SELECT extname FROM pg_extension WHERE extname = 'vector';")
        vector_ext = cur.fetchone()
        
        if vector_ext:
            print("✓ pgvector extension is installed")
        else:
            print("✗ pgvector extension is not installed")
            return False
        
        # Test tables
        cur.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name IN ('triples', 'documents');
        """)
        tables = cur.fetchall()
        table_names = [table[0] for table in tables]
        
        if 'triples' in table_names:
            print("✓ triples table exists")
        else:
            print("✗ triples table does not exist")
            
        if 'documents' in table_names:
            print("✓ documents table exists")
        else:
            print("✗ documents table does not exist")
        
        cur.close()
        conn.close()
        print("✓ Database connection test successful")
        return True
        
    except Exception as e:
        print(f"✗ Database connection test failed: {e}")
        return False

def main():
    """Main initialization function."""
    print("Initializing Graph-RAG database...")
    
    config = get_db_config()
    print(f"Database config: {config['host']}:{config['port']}/{config['database']}")
    
    # Step 1: Create database if it doesn't exist
    if not create_database_if_not_exists(config):
        print("Failed to create database. Exiting.")
        sys.exit(1)
    
    # Step 2: Set up pgvector extension
    if not setup_pgvector_extension(config):
        print("Failed to set up pgvector extension. Exiting.")
        sys.exit(1)
    
    # Step 3: Create base tables
    if not create_base_tables(config):
        print("Failed to create base tables. Exiting.")
        sys.exit(1)
    
    # Step 4: Test the setup
    if not test_connection(config):
        print("Database setup verification failed. Exiting.")
        sys.exit(1)
    
    print("\n🎉 Graph-RAG database initialization completed successfully!")
    print("\nNext steps:")
    print("1. Install spaCy language model: python -m spacy download en_core_web_sm")
    print("2. Set your OpenAI API key: export OPENAI_API_KEY=your_key_here")
    print("3. Start processing documents through the Graph-RAG pipeline")

if __name__ == "__main__":
    main()
