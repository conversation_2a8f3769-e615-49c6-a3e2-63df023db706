from fastapi import APIRouter, WebSocket, WebSocketDisconnect, UploadFile, File, HTTPException
from app.managers.ProjectManager import ProjectManager
from app.utils.csv_handler import process_csv_simple
import tempfile
import os

router = APIRouter()

@router.websocket("/ws/project/{project_id}")
async def project_ws(websocket: WebSocket, project_id: str):
    project = ProjectManager.from_project_id(project_id, base_dir="./projects")
    if not project:
        await websocket.accept()
        await websocket.send_json({"error": "Project does not exist or is unavailable."})
        await websocket.close(code=4001)
        return
    await websocket.accept()

    # Send connection confirmation
    await websocket.send_json({
        "type": "connection",
        "status": "connected",
        "project_id": project_id,
        "message": "🚀 Connected to fantastic reasoning AI!"
    })

    try:
        while True:
            data = await websocket.receive_text()

            # Send acknowledgment
            await websocket.send_json({
                "type": "message_received",
                "status": "processing",
                "message": "🧠 Processing your request..."
            })

            # Get the base agent and use streaming reply
            base_agent = project.agent_manager.get_agent("base")
            if base_agent:
                try:
                    # The agent's reply method now handles all websocket communication,
                    # including sending the final message.
                    base_agent.reply(data, websocket=websocket, max_tool_calls=15)
                except Exception as e:
                    await websocket.send_json({
                        "type": "error",
                        "message": f"Processing error: {str(e)}"
                    })
            else:
                await websocket.send_json({
                    "type": "error",
                    "message": "Agent not available"
                })

    except WebSocketDisconnect:
        pass  # ProjectManager will be cleaned up automatically

@router.post("/project/{project_id}/upload")
async def upload_input_file(project_id: str, file: UploadFile = File(...)):
    """Simple upload: CSV/Excel -> metadata extraction + RAG, TXT/MD -> direct RAG"""
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Read and save file to input folder
        content = await file.read()
        saved_path = project.save_file(file.filename, content, location="input")
        print(f"Saved file to {saved_path}")

        # Get file extension
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''

        # Process based on file type
        if file_ext in ['csv', 'xlsx', 'xls']:
            # CSV/Excel: Extract metadata and RAG the metadata
            result = process_csv_simple(saved_path, project_id)
            return {
                "filename": file.filename,
                "saved_path": saved_path,
                "type": "csv_metadata",
                "success": result["success"],
                "message": result.get("message", result.get("error"))
            }

        elif file_ext in ['txt', 'md']:
            # TXT/MD: Direct RAG
            base_agent = project.agent_manager.get_agent("base")
            if base_agent and base_agent.document_manager:
                result = base_agent.document_manager.process_file_upload(
                    content, file.filename, {"project_id": project_id}
                )
                return {
                    "filename": file.filename,
                    "saved_path": saved_path,
                    "type": "direct_rag",
                    "success": result.get("status") == "success",
                    "message": "File processed and indexed"
                }

        # Other files: just save
        return {
            "filename": file.filename,
            "saved_path": saved_path,
            "type": "saved_only",
            "success": True,
            "message": "File saved to input folder"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.post("/project/{project_id}/search")
async def search_project(project_id: str, query: str):
    """Simple search endpoint for Graph-RAG queries."""
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Use the agent's reply method which will automatically use Graph-RAG
        response = project.agent_manager.generate_response(query)

        return {
            "query": query,
            "response": response,
            "project_id": project_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")