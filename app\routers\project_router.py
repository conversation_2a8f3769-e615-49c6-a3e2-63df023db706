from fastapi import APIRouter, WebSocket, WebSocketDisconnect, UploadFile, File, HTTPException
from app.managers.ProjectManager import ProjectManager
from app.utils.csv_handler import process_csv_simple
import tempfile
import os

router = APIRouter()

@router.websocket("/ws/project/{project_id}")
async def project_ws(websocket: WebSocket, project_id: str):
    project = ProjectManager.from_project_id(project_id, base_dir="./projects")
    if not project:
        await websocket.accept()
        await websocket.send_json({"error": "Project does not exist or is unavailable."})
        await websocket.close(code=4001)
        return
    await websocket.accept()

    # Send connection confirmation
    await websocket.send_json({
        "type": "connection",
        "status": "connected",
        "project_id": project_id,
        "message": "🚀 Connected to fantastic reasoning AI!"
    })

    try:
        while True:
            data = await websocket.receive_text()

            # Send acknowledgment
            await websocket.send_json({
                "type": "message_received",
                "status": "processing",
                "message": "🧠 Processing your request..."
            })

            # Get the base agent and use streaming reply
            base_agent = project.agent_manager.get_agent("base")
            if base_agent:
                try:
                    # The agent's reply method now handles all websocket communication,
                    # including sending the final message.
                    base_agent.reply(data, websocket=websocket, max_tool_calls=15)
                except Exception as e:
                    await websocket.send_json({
                        "type": "error",
                        "message": f"Processing error: {str(e)}"
                    })
            else:
                await websocket.send_json({
                    "type": "error",
                    "message": "Agent not available"
                })

    except WebSocketDisconnect:
        pass  # ProjectManager will be cleaned up automatically

@router.post("/project/{project_id}/upload")
async def upload_input_file(project_id: str, file: UploadFile = File(...)):
    print(f"Uploading file {file.filename} to project {project_id}")
    try:
        # Get the project using existing pattern
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Read file content
        content = await file.read()

        # Save file to input folder (existing functionality)
        saved_path = project.save_file(file.filename, content, location="input")

        # Smart file processing based on type
        processing_result = None
        file_ext = file.filename.lower().split('.')[-1] if '.' in file.filename else ''

        if file_ext == 'csv':
            # Smart CSV handling using the available csv_handler functions
            try:
                # Process CSV using the available function
                result = process_csv_simple(saved_path, project_id)
                
                if result["success"]:
                    processing_result = {
                        "status": "success",
                        "file_type": "csv",
                        "location": f"input/{file.filename}",
                        "metadata_indexed": True,
                        "message": result["message"]
                    }
                else:
                    processing_result = {"status": "error", "error": result["error"]}

            except Exception as e:
                processing_result = {"status": "error", "error": f"CSV processing failed: {str(e)}"}

        else:
            # Regular file processing through RAG
            try:
                base_agent = project.agent_manager.get_agent("base")
                if base_agent and base_agent.document_manager:
                    result = base_agent.document_manager.process_file_upload(
                        content,
                        file.filename,
                        {"project_id": project_id}
                    )
                    processing_result = result
            except Exception as e:
                processing_result = {"status": "error", "error": str(e)}

        response = {
            "filename": file.filename,
            "status": "uploaded to input folder",
            "saved_path": saved_path
        }

        if processing_result:
            response["processing"] = processing_result

        return response

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")

@router.post("/project/{project_id}/search")
async def search_project(project_id: str, query: str):
    """Simple search endpoint for Graph-RAG queries."""
    try:
        project = ProjectManager.from_project_id(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        # Use the agent's reply method which will automatically use Graph-RAG
        response = project.agent_manager.generate_response(query)

        return {
            "query": query,
            "response": response,
            "project_id": project_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")