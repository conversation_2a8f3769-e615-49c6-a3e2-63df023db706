#!/usr/bin/env python3
"""
Test script to verify vector search is working.
"""

import os
from app.managers.DocumentManager import Document<PERSON>anager

def test_vector_search():
    """Test if vector search is working properly."""
    
    print("🧪 Testing vector search functionality...")
    
    try:
        # Create DocumentManager
        doc_manager = DocumentManager(project_id="test")
        
        if not doc_manager.vector_store:
            print("❌ Vector store not initialized")
            return False
        
        print("✅ Vector store initialized")
        
        # Test adding a simple document
        print("📝 Adding test document...")
        test_content = """
        Machine Learning Model Training Results

        Our final model achieved an accuracy of 94.2% on the test dataset.
        The training process involved multiple epochs with careful hyperparameter tuning.

        Key findings:
        - Learning rate: 0.001 worked best
        - Batch size: 32 provided optimal performance
        - The model converged after 150 epochs
        - Validation accuracy peaked at 92.8%

        The final accuracy represents a significant improvement over baseline models.
        """
        
        result = doc_manager.process_text_content(
            test_content,
            "test_ml_results.txt",
            {"test": True}
        )
        
        print(f"📄 Document processing result: {result}")
        
        if result.get("status") != "success":
            print("❌ Document processing failed")
            return False
        
        print("✅ Document processed successfully")
        
        # Test vector search
        print("🔍 Testing vector search...")
        
        test_queries = [
            "final accuracy",
            "model performance",
            "training results",
            "learning rate",
            "validation accuracy"
        ]
        
        for query in test_queries:
            print(f"\n🔎 Searching for: '{query}'")
            search_results = doc_manager.vector_search(query, k=3)
            
            print(f"   Found {len(search_results)} results")
            
            for i, result in enumerate(search_results):
                print(f"   {i+1}. Score: {result['score']:.3f}")
                print(f"      Content: {result['content'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_vector_search()
    if success:
        print("\n🎉 Vector search test completed!")
    else:
        print("\n💥 Vector search test failed!")
