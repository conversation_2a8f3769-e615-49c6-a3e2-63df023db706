# 🚀 Fantastic Compatible Provider System

Kairos backend now supports multiple AI providers with seamless switching between OpenAI and Google/Gemini services. The system is designed to be plug-and-play with minimal configuration changes.

## 🎯 Supported Providers

### LLM Providers
- **OpenAI** - GPT models with excellent tool support
- **Gemini** - Google's Gemini models using OpenAI-compatible API

### Embedding Providers  
- **OpenAI** - High-quality text embeddings
- **Google** - Google's embedding models
- **Offline** - Local sentence-transformers (no API key required)

## ⚡ Quick Setup

### 1. Configure Environment Variables

Copy the example environment file:
```bash
cp .env.example .env
```

Edit `.env` with your preferred configuration:

```bash
# For Gemini + Google Embeddings (Recommended)
LLM_PROVIDER=gemini
EMBEDDING_PROVIDER=google
GOOGLE_API_KEY=your_google_api_key_here

# For OpenAI (Alternative)
LLM_PROVIDER=openai
EMBEDDING_PROVIDER=openai
OPENAI_API_KEY=your_openai_api_key_here

# Mixed setup (OpenAI LLM + Google Embeddings)
LLM_PROVIDER=openai
EMBEDDING_PROVIDER=google
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

### 2. Test Your Configuration

Run the configuration checker:
```bash
python -m app.config.providers
```

Run the full test suite:
```bash
python test_providers.py
```

### 3. Start the Application

```bash
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 Provider Details

### Gemini Provider
- Uses OpenAI-compatible API endpoint
- Full tool/function calling support
- Models: `gemini-1.5-flash`, `gemini-1.5-pro`, `gemini-pro`
- Requires: `GOOGLE_API_KEY`

### OpenAI Provider
- Native OpenAI API
- Full tool/function calling support  
- Models: `gpt-4o-mini`, `gpt-4o`, `gpt-3.5-turbo`
- Requires: `OPENAI_API_KEY`

### Google Embeddings
- Native Google AI API
- High-quality embeddings
- Models: `models/text-embedding-004`, `models/embedding-001`
- Requires: `GOOGLE_API_KEY`

### OpenAI Embeddings
- Native OpenAI API
- Industry-standard embeddings
- Models: `text-embedding-3-small`, `text-embedding-3-large`
- Requires: `OPENAI_API_KEY`

### Offline Embeddings
- Local sentence-transformers
- No API key required
- Models: `sentence-transformers/all-MiniLM-L6-v2`
- Automatic fallback option

## 🎛️ Advanced Configuration

### Custom Models

Set specific models via environment variables:
```bash
LLM_MODEL=gemini-1.5-pro
EMBEDDING_MODEL=text-embedding-3-large
```

### Programmatic Configuration

```python
from app.agents.BaseAgent import BaseAgent

# Create agent with specific providers
agent = BaseAgent(
    project_id="my-project",
    llm_provider="gemini",
    model_name="gemini-1.5-flash"
)
```

## 🔍 Troubleshooting

### Check Configuration Status
```bash
python -m app.config.providers
```

### Common Issues

1. **Missing API Keys**
   - Ensure your API keys are set in `.env`
   - Check that `.env` file is in the project root

2. **Provider Not Found**
   - Verify provider names: `openai`, `gemini`, `google`, `offline`
   - Check spelling in environment variables

3. **Import Errors**
   - Install required dependencies: `pip install -r requirements.txt`
   - For Google providers: `pip install google-generativeai`

### Debug Mode

Enable detailed logging:
```bash
DEBUG=True
LOG_LEVEL=DEBUG
```

## 🚀 Benefits

✅ **Plug-and-Play**: Switch providers with just environment variables  
✅ **OpenAI Compatible**: Gemini uses OpenAI-compatible API for seamless integration  
✅ **Fallback Support**: Automatic fallback to offline embeddings  
✅ **Tool Support**: Full function calling support across providers  
✅ **Cost Optimization**: Choose providers based on cost and performance needs  
✅ **No Vendor Lock-in**: Easy migration between providers  

## 📊 Provider Comparison

| Feature | OpenAI | Gemini | Google Embeddings | Offline |
|---------|--------|--------|------------------|---------|
| Cost | $$$ | $$ | $$ | Free |
| Speed | Fast | Fast | Fast | Slow |
| Quality | Excellent | Excellent | Excellent | Good |
| Tools | ✅ | ✅ | N/A | N/A |
| Offline | ❌ | ❌ | ❌ | ✅ |

## 🎉 Success!

Your Kairos backend now has a fantastic compatible system supporting multiple AI providers. Switch between them effortlessly and choose the best provider for your specific needs!
