from typing import Dict
import json
import os
import subprocess
import requests
from bs4 import BeautifulSoup
from ..managers.DocumentManager import DocumentManager
from ..providers.llm import get_llm_provider
from ..providers.embeddings import get_embedding_provider

class BaseAgent:
    """Simple, powerful AI agent with Graph-RAG, web search, and code execution capabilities."""

    access_specifier: str = "General"
    model_name: str = "gemini-2.5-flash"  # Default to Gemini
    llm_provider_type: str = "gemini"     # Default provider type

    def __init__(self, project_id: str = None, model_name: str = None, access_specifier: str = None,
                 db_config: Dict = None, llm_provider: str = None):
        self.project_id = project_id
        self.model_name = model_name or self.model_name
        self.access_specifier = access_specifier or self.access_specifier
        self.llm_provider_type = llm_provider or os.getenv("LLM_PROVIDER", self.llm_provider_type)

        # Initialize components safely
        self.document_manager = None
        self.llm_provider = None

        # Try to initialize document manager if project_id provided
        if project_id:
            try:
                self.document_manager = DocumentManager(project_id=project_id, db_config=db_config)
            except Exception as e:
                print(f"Warning: Could not initialize DocumentManager: {e}")

        # Try to initialize LLM provider with correct provider type
        try:
            self.llm_provider = get_llm_provider(provider=self.llm_provider_type, model=self.model_name)
            print(f"✅ Initialized LLM provider: {self.llm_provider_type} with model: {self.model_name}")
        except Exception as e:
            print(f"Warning: Could not initialize LLM provider: {e}")

        # Create work directory
        if project_id:
            self.work_dir = f"projects/{project_id}/work"
            os.makedirs(self.work_dir, exist_ok=True)
        else:
            self.work_dir = "work"
            os.makedirs(self.work_dir, exist_ok=True)

        # Define tools
        self.tools = [
            {
                "type": "function",
                "function": {
                    "name": "project_search",
                    "description": "Search through project documents and knowledge base for relevant information. Use multiple searches to build comprehensive understanding.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query - be specific and try different angles"},
                            "k": {"type": "integer", "description": "Number of results (default: 5, max: 20)"}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "web_search",
                    "description": "Search the web for current information and scrape content from URLs. Always summarize findings and cite sources in your final answer.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query or URL to scrape - be specific for better results"},
                            "is_url": {"type": "boolean", "description": "True if query is a URL to scrape, False for web search"}
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "code_execution",
                    "description": "Execute Python code for data analysis, directory exploration, file processing, and learning project structure. Includes pandas, numpy, and helper functions for directory analysis.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "code": {"type": "string", "description": "Python code to execute. Use analyze_directory() and get_file_info() for project exploration."},
                            "filename": {"type": "string", "description": "Optional filename to save code for reuse"}
                        },
                        "required": ["code"]
                    }
                }
            }
        ]

    def _build_system_prompt(self) -> str:
        project_info = ""
        if self.project_id:
            project_info = f"""

PROJECT STRUCTURE:
- input/ : Uploaded files (CSV, documents, etc.)
- output/ : Analysis results and generated files
- work/ : Your code execution workspace
- metadata/ : File metadata and analysis info

When working with CSV files, use: pd.read_csv("input/filename.csv")
When saving results, use: "output/filename"
All code runs in the work/ directory.
"""

        return f"""You are a powerful AI assistant with access to:
- project_search: Search through uploaded project documents and file information
- web_search: Search the web or scrape specific URLs
- code_execution: Write and run Python code for data analysis

{project_info}
Always search for context before answering. Use tools to provide accurate, helpful responses.
Model: {self.model_name} | Project: {self.project_id or 'General'}"""

    def project_search(self, query: str, k: int = 5) -> str:
        """Search through project documents."""
        if not self.document_manager:
            return json.dumps({"error": "No project loaded"})

        try:
            results = self.document_manager.vector_search(query, k)
            return json.dumps({
                "found": len(results),
                "query": query,
                "results": [{"content": r["content"], "score": r["score"], "metadata": r.get("metadata", {})} for r in results]
            })
        except Exception as e:
            return json.dumps({"error": str(e)})

    def web_search(self, query: str, is_url: bool = False) -> str:
        """Search web or scrape URL."""
        try:
            if is_url:
                # Scrape specific URL
                response = requests.get(query, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')

                # Extract text content
                for script in soup(["script", "style"]):
                    script.decompose()
                text = soup.get_text()
                lines = (line.strip() for line in text.splitlines())
                text = '\n'.join(line for line in lines if line)

                return json.dumps({
                    "url": query,
                    "title": soup.title.string if soup.title else "No title",
                    "content": text[:2000]  # Limit content
                })
            else:
                # Simple web search using DuckDuckGo (no API key needed)
                search_url = f"https://duckduckgo.com/html/?q={query}"
                headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                response = requests.get(search_url, headers=headers, timeout=10)
                soup = BeautifulSoup(response.content, 'html.parser')

                results = []
                for result in soup.find_all('div', class_='result')[:5]:
                    title_elem = result.find('a', class_='result__a')
                    snippet_elem = result.find('a', class_='result__snippet')

                    if title_elem:
                        results.append({
                            "title": title_elem.get_text().strip(),
                            "url": title_elem.get('href', ''),
                            "snippet": snippet_elem.get_text().strip() if snippet_elem else ""
                        })

                return json.dumps({
                    "query": query,
                    "results": results
                })
        except Exception as e:
            return json.dumps({"error": str(e)})

    def code_execution(self, code: str, filename: str = None) -> str:
        """Execute Python code with enhanced capabilities for analysis and learning."""
        try:
            # Enhanced code with helpful imports and project context
            enhanced_code = f"""
import os
import sys
import json
import pandas as pd
import numpy as np
from pathlib import Path
import subprocess
import glob

# Add project root to path for imports
project_root = r'{os.path.abspath(".")}'
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Helper functions for directory analysis
def analyze_directory(path='.', max_depth=3):
    \"\"\"Analyze directory structure and return insights.\"\"\"
    structure = {{}}
    for root, dirs, files in os.walk(path):
        level = root.replace(path, '').count(os.sep)
        if level >= max_depth:
            dirs[:] = []  # Don't go deeper
            continue
        indent = ' ' * 2 * level
        structure[root] = {{
            'dirs': dirs,
            'files': files,
            'file_count': len(files),
            'dir_count': len(dirs)
        }}
    return structure

def get_file_info(pattern='*'):
    \"\"\"Get information about files matching pattern.\"\"\"
    files = glob.glob(pattern, recursive=True)
    return [{{
        'path': f,
        'size': os.path.getsize(f) if os.path.exists(f) else 0,
        'modified': os.path.getmtime(f) if os.path.exists(f) else 0
    }} for f in files]

# User code starts here:
{code}
"""

            # Save code to file if filename provided
            if filename:
                filepath = os.path.join(self.work_dir, filename)
                with open(filepath, 'w') as f:
                    f.write(enhanced_code)

            # Execute enhanced code and capture output
            result = subprocess.run(
                ['python', '-c', enhanced_code],
                cwd=self.work_dir,
                capture_output=True,
                text=True,
                timeout=60  # Increased timeout for analysis tasks
            )

            # Format output for better readability
            output = {
                "execution_successful": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode,
                "saved_to": filename if filename else None,
                "working_directory": self.work_dir
            }

            # If there's useful output, format it nicely
            if result.stdout:
                output["formatted_output"] = f"📊 Code Execution Results:\n{result.stdout}"

            if result.stderr and result.returncode != 0:
                output["error_details"] = f"❌ Error: {result.stderr}"

            return json.dumps(output, indent=2)

        except subprocess.TimeoutExpired:
            return json.dumps({
                "error": "Code execution timed out (60 seconds limit)",
                "code": code[:200] + "..." if len(code) > 200 else code
            })
        except Exception as e:
            return json.dumps({
                "error": f"Code execution failed: {str(e)}",
                "code": code[:200] + "..." if len(code) > 200 else code
            })
        except subprocess.TimeoutExpired:
            return json.dumps({"error": "Code execution timed out"})
        except Exception as e:
            return json.dumps({"error": str(e)})

    async def reply(self, message: str, context: Dict = None, max_tool_calls: int = 15, websocket=None) -> str:
        """Main method to handle user messages with advanced reasoning and tool chaining."""
        if not self.llm_provider:
            return "Error: LLM provider not initialized. Please check your API keys."

        try:
            print(f"🧠 BaseAgent.reply called with message: {message[:100]}...")
            print(f"🔧 Max tool calls: {max_tool_calls}, WebSocket: {'Yes' if websocket else 'No'}")

            result = await self._reasoning_loop(message, max_tool_calls, websocket)
            result = result or "No response generated"  # Fix null content
            print(f"📊 Reasoning loop returned {len(result)} characters")
            return result
        except Exception as e:
            print(f"❌ Error in reply: {e}")
            import traceback
            traceback.print_exc()
            return f"Error: {str(e)}"

    async def _reasoning_loop(self, original_message: str, max_tool_calls: int = 15, websocket=None) -> str:
        """Advanced reasoning loop with tool chaining and self-reprompting."""
        system_prompt = self._build_system_prompt()

        # Enhanced system prompt for reasoning
        enhanced_system = f"""{system_prompt}

🧠 REASONING INSTRUCTIONS:
- You are a fantastic reasoning AI that MUST use tools iteratively and follow up
- NEVER stop after just one tool call - always analyze results and continue reasoning
- After EVERY tool use, you MUST think about what you learned and what to do next
- For project searches: If you find results, analyze them and search for more specific details
- For web searches: Summarize findings, cite sources, and search for additional context if needed
- For code execution: Use results for further analysis, run additional code if helpful
- You can make up to {max_tool_calls} tool calls - USE THEM to be thorough
- ALWAYS provide a comprehensive final answer that incorporates ALL tool results
- Be a GOOD BOY and follow up on every tool result with analysis or additional searches"""

        # Add explicit tool usage encouragement to user message
        enhanced_user_message = f"""{original_message}

IMPORTANT: You MUST use tools to research and analyze before providing your final answer. Start by using appropriate tools like project_search, web_search, or code_execution to gather information, then analyze the results and continue reasoning."""

        messages = [
            {"role": "system", "content": enhanced_system},
            {"role": "user", "content": enhanced_user_message}
        ]

        tool_call_count = 0
        conversation_history = []

        # Send initial thinking message
        if websocket:
            await self._send_websocket_message(websocket, {
                "type": "thinking",
                "content": "🧠 Starting reasoning process...",
                "status": "processing"
            })

        while tool_call_count < max_tool_calls:
            print(f"🔄 Reasoning loop iteration {tool_call_count + 1}/{max_tool_calls}")

            # Send reasoning status
            if websocket:
                await self._send_websocket_message(websocket, {
                    "type": "reasoning_step",
                    "content": f"🔄 Reasoning loop iteration {tool_call_count + 1}/{max_tool_calls}",
                    "status": "thinking"
                })

            # Get response from LLM
            response = self.llm_provider.chat(
                messages=messages,
                tools=self.tools,
                tool_choice="auto",
                max_tokens=1500,
                temperature=0.7
            )

            # If no tool calls, we have our final answer
            if not response.get("tool_calls"):
                final_content = response.get("content") or "No response generated"

                # Send final answer (no more enhancement - keep it simple)
                if websocket:
                    await self._send_websocket_message(websocket, {
                        "type": "final_message",
                        "content": final_content or "No response generated",
                        "tools_used": len(conversation_history) if conversation_history else 0,
                        "status": "completed"
                    })
                return final_content or "No response generated"

            # Execute tool calls
            assistant_message = response.get("content", "")
            tool_calls = response.get("tool_calls", [])

            # Add assistant message to conversation (ensure content is not empty)
            messages.append({
                "role": "assistant",
                "content": assistant_message or "I'll use tools to help answer your question.",
                "tool_calls": tool_calls
            })

            # Execute each tool call
            for tool_call in tool_calls:
                tool_call_count += 1
                if tool_call_count > max_tool_calls:
                    break

                # Stream tool execution start
                if websocket:
                    await self._send_websocket_message(websocket, {
                        "type": "tool_start",
                        "tool": tool_call.function.name,
                        "args": json.loads(tool_call.function.arguments),
                        "status": "executing"
                    })

                result = self._execute_tool_call(tool_call)

                # Stream tool execution result
                if websocket:
                    await self._send_websocket_message(websocket, {
                        "type": "tool_result",
                        "tool": tool_call.function.name,
                        "result": result,
                        "status": "completed"
                    })

                # Add tool result to conversation (ensure content is not empty)
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": result or "Tool executed successfully"
                })

                # Track conversation for final enhancement
                conversation_history.append({
                    "tool": tool_call.function.name,
                    "args": tool_call.function.arguments,
                    "result": result
                })

            # Add follow-up prompt to encourage continued reasoning
            if tool_calls and tool_call_count < max_tool_calls:
                follow_up_prompt = f"""Now analyze the tool results above and continue reasoning. You have {max_tool_calls - tool_call_count} more tool calls available.

What did you learn? What should you do next? Use more tools or provide your final answer."""

                messages.append({
                    "role": "user",
                    "content": follow_up_prompt
                })

                # Send follow-up message via websocket
                if websocket:
                    await self._send_websocket_message(websocket, {
                        "type": "follow_up",
                        "content": "🤔 Analyzing results and continuing reasoning...",
                        "remaining_calls": max_tool_calls - tool_call_count,
                        "status": "reasoning"
                    })

            # Continue the reasoning loop
            if tool_call_count >= max_tool_calls:
                break

        # If we hit max tool calls, force a final answer
        final_response = self.llm_provider.chat(
            messages=messages + [{
                "role": "user",
                "content": "Please provide your final, comprehensive answer based on all the tool results above."
            }],
            max_tokens=2000,
            temperature=0.7
        )

        final_content = final_response.get("content") or "Maximum tool calls reached. Please try a more specific question."

        # Stream final response
        if websocket:
            await self._send_websocket_message(websocket, {
                "type": "final_message",
                "content": final_content or "No response generated",
                "tool_calls_used": tool_call_count,
                "status": "completed"
            })

        return final_content

    async def _send_websocket_message(self, websocket, message):
        """Helper to send WebSocket messages safely."""
        try:
            # Ensure content is never None
            if "content" in message and message["content"] is None:
                message["content"] = "No content"
            await websocket.send_json(message)
        except Exception as e:
            print(f"WebSocket send error: {e}")

    def _execute_tool_call(self, tool_call) -> str:
        """Execute a single tool call and return formatted result."""
        try:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)

            if function_name == "project_search":
                result = self.project_search(function_args.get("query", ""), function_args.get("k", 5))
            elif function_name == "web_search":
                result = self.web_search(function_args.get("query", ""), function_args.get("is_url", False))
            elif function_name == "code_execution":
                result = self.code_execution(function_args.get("code", ""), function_args.get("filename"))
            else:
                result = json.dumps({"error": f"Unknown function: {function_name}"})

            return result

        except Exception as e:
            return json.dumps({"error": f"Tool execution failed: {str(e)}"})

